#!/usr/bin/env node

/**
 * Smart Office Assistant - Complete Parking System Test
 * 
 * This script performs a comprehensive test of the entire parking system
 * including booking, cancellation, and edge cases.
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();
if (fs.existsSync('.env.seed')) {
  require('dotenv').config({ path: '.env.seed' });
}

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Test complete parking workflow
 */
async function testCompleteParkingWorkflow() {
  console.log('🚗 Testing complete parking workflow...\n');

  try {
    // Step 1: Authenticate as test user
    console.log('🔐 Step 1: Authenticating as test user...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'UserPass123!'
    });

    if (authError) throw authError;
    console.log(`   ✅ Authenticated as: ${authData.user.email}`);

    const today = new Date().toISOString().split('T')[0];

    // Step 2: Check current reservations
    console.log('\n📋 Step 2: Checking current reservations...');
    const { data: existingReservations, error: checkError } = await supabase
      .from('parking_reservations')
      .select(`
        id,
        status,
        parking_spots (spot_number, spot_type)
      `)
      .eq('user_id', authData.user.id)
      .eq('reservation_date', today)
      .eq('status', 'active');

    if (checkError) throw checkError;

    if (existingReservations.length > 0) {
      console.log(`   📍 Found ${existingReservations.length} existing reservation(s)`);
      
      // Cancel existing reservations for clean test
      for (const reservation of existingReservations) {
        const { error: cancelError } = await supabase
          .from('parking_reservations')
          .update({ status: 'cancelled' })
          .eq('id', reservation.id);

        if (cancelError) throw cancelError;
        
        const spotNumber = `${reservation.parking_spots.spot_type.toUpperCase()}-${reservation.parking_spots.spot_number}`;
        console.log(`   🧹 Cancelled existing reservation: ${spotNumber}`);
      }
    } else {
      console.log('   ✅ No existing reservations found');
    }

    // Step 3: Find available parking spots
    console.log('\n🔍 Step 3: Finding available parking spots...');
    const { data: availableSpots, error: spotsError } = await supabase
      .from('parking_spots')
      .select(`
        id,
        spot_number,
        spot_type,
        parking_reservations!left (
          id,
          status,
          reservation_date
        )
      `)
      .eq('is_active', true)
      .eq('parking_reservations.reservation_date', today)
      .eq('parking_reservations.status', 'active')
      .limit(5);

    if (spotsError) throw spotsError;

    const trulyAvailableSpots = availableSpots.filter(spot => 
      !spot.parking_reservations || spot.parking_reservations.length === 0
    );

    console.log(`   📊 Found ${trulyAvailableSpots.length} available spots`);

    if (trulyAvailableSpots.length === 0) {
      console.log('   ⚠️ No available spots for testing');
      return;
    }

    // Step 4: Book a parking spot
    const testSpot = trulyAvailableSpots[0];
    const spotNumber = `${testSpot.spot_type.toUpperCase()}-${testSpot.spot_number}`;
    
    console.log(`\n📝 Step 4: Booking parking spot ${spotNumber}...`);
    const { data: newReservation, error: bookingError } = await supabase
      .from('parking_reservations')
      .insert({
        user_id: authData.user.id,
        parking_spot_id: testSpot.id,
        reservation_date: today,
        start_time: '09:00:00',
        status: 'active'
      })
      .select(`
        id,
        status,
        parking_spots (spot_number, spot_type)
      `)
      .single();

    if (bookingError) throw bookingError;
    console.log(`   ✅ Successfully booked spot: ${spotNumber}`);
    console.log(`   📋 Reservation ID: ${newReservation.id}`);

    // Step 5: Verify booking
    console.log('\n✅ Step 5: Verifying booking...');
    const { data: verifyReservation, error: verifyError } = await supabase
      .from('parking_reservations')
      .select(`
        id,
        status,
        user_id,
        parking_spots (spot_number, spot_type)
      `)
      .eq('id', newReservation.id)
      .single();

    if (verifyError) throw verifyError;

    if (verifyReservation.status === 'active' && verifyReservation.user_id === authData.user.id) {
      console.log('   ✅ Booking verified successfully');
    } else {
      console.log('   ❌ Booking verification failed');
    }

    // Step 6: Test cancellation
    console.log('\n🔄 Step 6: Testing parking spot cancellation...');
    const { data: cancelledReservation, error: cancelError } = await supabase
      .from('parking_reservations')
      .update({ status: 'cancelled' })
      .eq('id', newReservation.id)
      .select()
      .single();

    if (cancelError) throw cancelError;
    console.log('   ✅ Cancellation successful');
    console.log(`   📊 New status: ${cancelledReservation.status}`);

    // Step 7: Verify spot is available again
    console.log('\n🔍 Step 7: Verifying spot availability after cancellation...');
    const { data: spotCheck, error: spotCheckError } = await supabase
      .from('parking_spots')
      .select(`
        id,
        spot_number,
        spot_type,
        parking_reservations!left (
          id,
          status,
          reservation_date
        )
      `)
      .eq('id', testSpot.id)
      .eq('parking_reservations.reservation_date', today)
      .eq('parking_reservations.status', 'active');

    if (spotCheckError) throw spotCheckError;

    const hasActiveReservation = spotCheck[0]?.parking_reservations?.length > 0;
    
    if (!hasActiveReservation) {
      console.log('   ✅ Spot is now available for booking');
    } else {
      console.log('   ⚠️ Spot still shows as occupied');
    }

    // Step 8: Test booking the same spot again
    console.log('\n🔄 Step 8: Testing rebooking the same spot...');
    const { data: rebooking, error: rebookingError } = await supabase
      .from('parking_reservations')
      .insert({
        user_id: authData.user.id,
        parking_spot_id: testSpot.id,
        reservation_date: today,
        start_time: '10:00:00',
        status: 'active'
      })
      .select()
      .single();

    if (rebookingError) throw rebookingError;
    console.log('   ✅ Successfully rebooked the same spot');

    // Clean up - cancel the test reservation
    const { error: cleanupError } = await supabase
      .from('parking_reservations')
      .update({ status: 'cancelled' })
      .eq('id', rebooking.id);

    if (cleanupError) throw cleanupError;
    console.log('   🧹 Cleaned up test reservation');

    // Step 9: Sign out
    console.log('\n🚪 Step 9: Signing out...');
    await supabase.auth.signOut();
    console.log('   ✅ Signed out successfully');

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('🎉 COMPLETE PARKING WORKFLOW TEST SUMMARY');
    console.log('='.repeat(60));
    console.log('✅ User authentication: Working');
    console.log('✅ Check existing reservations: Working');
    console.log('✅ Find available spots: Working');
    console.log('✅ Book parking spot: Working');
    console.log('✅ Verify booking: Working');
    console.log('✅ Cancel reservation: Working');
    console.log('✅ Verify spot availability: Working');
    console.log('✅ Rebook same spot: Working');
    console.log('✅ Cleanup: Working');
    console.log('\n🎯 All parking functionality is working correctly!');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    console.error('\n🔍 Error details:');
    console.error('- Code:', error.code);
    console.error('- Details:', error.details);
    console.error('- Hint:', error.hint);
  }
}

/**
 * Test edge cases and error scenarios
 */
async function testEdgeCases() {
  console.log('\n🧪 Testing edge cases and error scenarios...\n');

  try {
    // Test 1: Double booking prevention
    console.log('🚫 Test 1: Double booking prevention...');
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'UserPass123!'
    });

    if (authError) throw authError;

    const today = new Date().toISOString().split('T')[0];

    // Find an available spot
    const { data: availableSpots, error: spotsError } = await supabase
      .from('parking_spots')
      .select('id, spot_number, spot_type')
      .eq('is_active', true)
      .limit(1);

    if (spotsError) throw spotsError;

    if (availableSpots.length > 0) {
      const testSpot = availableSpots[0];
      
      // Create first reservation
      const { data: firstReservation, error: firstError } = await supabase
        .from('parking_reservations')
        .insert({
          user_id: authData.user.id,
          parking_spot_id: testSpot.id,
          reservation_date: today,
          start_time: '09:00:00',
          status: 'active'
        })
        .select()
        .single();

      if (firstError) throw firstError;

      // Try to create second reservation for same user
      const { error: secondError } = await supabase
        .from('parking_reservations')
        .insert({
          user_id: authData.user.id,
          parking_spot_id: testSpot.id,
          reservation_date: today,
          start_time: '10:00:00',
          status: 'active'
        });

      if (secondError) {
        console.log('   ✅ Double booking correctly prevented');
      } else {
        console.log('   ⚠️ Double booking was allowed (check constraints)');
      }

      // Clean up
      await supabase
        .from('parking_reservations')
        .update({ status: 'cancelled' })
        .eq('id', firstReservation.id);
    }

    await supabase.auth.signOut();
    console.log('   ✅ Edge case testing completed');

  } catch (error) {
    console.log('   ⚠️ Edge case test error:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  testCompleteParkingWorkflow()
    .then(() => testEdgeCases())
    .catch(error => {
      console.error('💥 Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testCompleteParkingWorkflow };
